"""
UI Components for Psychiatric Assessment System

This module contains reusable UI components, form patterns, and styling functions
that ensure consistent behavior across all sections of the application.
"""

import streamlit as st
from typing import List, Dict, Any, Optional, Callable
from data_models import DSM5_SUBSTANCE_CATEGORIES, PSYCHIATRIC_MEDICATION_CLASSES, get_dsm5_severity, get_severity_class

def render_modern_css():
    """Render modern, clean CSS for the application"""
    st.markdown("""
    <style>
    /* Modern Color Scheme */
    :root {
        --primary-blue: #2563eb;
        --primary-blue-light: #3b82f6;
        --primary-blue-dark: #1d4ed8;
        --secondary-purple: #7c3aed;
        --accent-green: #10b981;
        --accent-orange: #f59e0b;
        --accent-red: #ef4444;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;
    }
    
    /* Main Headers */
    .main-header {
        font-size: 2.5rem;
        font-weight: 800;
        color: var(--primary-blue);
        text-align: center;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-purple) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* Section Headers */
    .section-header {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--primary-blue-dark);
        border-left: 5px solid var(--primary-blue);
        padding: 1rem 1.5rem;
        margin: 1.5rem 0;
        background: linear-gradient(90deg, rgba(37,99,235,0.1) 0%, transparent 100%);
        border-radius: 0 10px 10px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    /* Form Containers */
    .form-container {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
    }
    
    .form-container:hover {
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-blue-light);
    }
    
    /* Success Messages */
    .success-message {
        background: linear-gradient(135deg, var(--accent-green), #059669);
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        font-weight: 600;
        text-align: center;
    }
    
    /* Substance Category Cards */
    .substance-card {
        background: var(--gray-50);
        border: 2px solid var(--gray-200);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .substance-card:hover {
        border-color: var(--primary-blue);
        background: rgba(37,99,235,0.05);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .substance-card.selected {
        border-color: var(--primary-blue);
        background: rgba(37,99,235,0.1);
        box-shadow: 0 4px 12px rgba(37,99,235,0.2);
    }
    
    /* Severity Indicators */
    .severity-indicator {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: bold;
        text-align: center;
        margin: 0.5rem 0;
        display: inline-block;
        min-width: 120px;
    }
    
    .severity-mild { 
        background: linear-gradient(135deg, #d1fae5, #a7f3d0); 
        color: #065f46; 
        border: 1px solid #10b981;
    }
    .severity-moderate { 
        background: linear-gradient(135deg, #fef3c7, #fde68a); 
        color: #92400e; 
        border: 1px solid #f59e0b;
    }
    .severity-severe { 
        background: linear-gradient(135deg, #fee2e2, #fecaca); 
        color: #991b1b; 
        border: 1px solid #ef4444;
    }
    .severity-none { 
        background: linear-gradient(135deg, var(--gray-100), var(--gray-200)); 
        color: var(--gray-700); 
        border: 1px solid var(--gray-300);
    }
    
    /* Enhanced Buttons */
    .stButton > button {
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-purple));
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 700;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        background: linear-gradient(135deg, var(--primary-blue-dark), var(--secondary-purple));
    }
    
    /* Form Submit Buttons */
    .stFormSubmitButton > button {
        background: linear-gradient(135deg, var(--accent-green), #059669);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        width: 100%;
        margin-top: 1rem;
    }
    
    .stFormSubmitButton > button:hover {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
    }
    
    /* Enhanced Multiselect */
    .stMultiSelect > div > div {
        background-color: white;
        border: 2px solid var(--gray-200);
        border-radius: 8px;
        transition: border-color 0.3s ease;
    }
    
    .stMultiSelect > div > div:focus-within {
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    
    /* Multiselect Tags */
    .stMultiSelect [data-baseweb="tag"] {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
        color: white;
        border-radius: 15px;
        margin: 2px;
        padding: 4px 12px;
        font-weight: 500;
    }
    
    /* Progress Indicators */
    .progress-container {
        background: var(--gray-50);
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        border: 1px solid var(--gray-200);
    }
    
    /* Clinical Guidance */
    .clinical-guidance {
        background: linear-gradient(135deg, #eff6ff, #dbeafe);
        border-left: 4px solid var(--primary-blue);
        padding: 1rem 1.5rem;
        margin: 1rem 0;
        border-radius: 0 8px 8px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .clinical-guidance h4 {
        color: var(--primary-blue-dark);
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
        font-weight: 600;
    }
    
    /* Auto-save Indicator */
    .auto-save-indicator {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: var(--accent-green);
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        font-weight: 600;
        animation: slideIn 0.3s ease;
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .main-header {
            font-size: 2rem;
        }
        
        .section-header {
            font-size: 1.5rem;
            padding: 0.75rem 1rem;
        }
        
        .form-container {
            padding: 1rem;
        }
        
        .substance-card {
            padding: 1rem;
        }
    }
    </style>
    """, unsafe_allow_html=True)

def create_form_section(title: str, form_key: str, content_func: Callable, 
                       expanded: bool = False, help_text: Optional[str] = None) -> bool:
    """
    Create a form section with consistent styling and behavior.
    This is the successful pattern from the Present Illness section.
    
    Args:
        title: Section title
        form_key: Unique key for the form
        content_func: Function that renders the form content and returns data
        expanded: Whether the expander should be expanded by default
        help_text: Optional help text to display
    
    Returns:
        bool: True if form was submitted successfully
    """
    with st.expander(title, expanded=expanded):
        if help_text:
            st.markdown(f"""
            <div class="clinical-guidance">
                <h4>💡 Clinical Guidance</h4>
                <p>{help_text}</p>
            </div>
            """, unsafe_allow_html=True)
        
        with st.form(form_key):
            # Call the content function to render form elements
            form_data = content_func()
            
            # Submit button
            submitted = st.form_submit_button(
                f"💾 Update {title}", 
                use_container_width=True
            )
            
            if submitted:
                st.markdown(f"""
                <div class="success-message">
                    ✅ {title} updated successfully!
                </div>
                """, unsafe_allow_html=True)
                
                # Auto-save if enabled
                if st.session_state.get('auto_save_enabled', True):
                    try:
                        # Import here to avoid circular imports
                        from database import save_assessment_data
                        save_assessment_data(st.session_state.patient_data)
                        st.info("💾 Auto-saved to database")
                    except Exception as e:
                        st.warning(f"Auto-save failed: {str(e)}")
                
                return True
    
    return False

def create_substance_card(substance_name: str, icon: str, description: str, 
                         is_selected: bool = False) -> bool:
    """Create an interactive substance selection card"""
    card_class = "substance-card selected" if is_selected else "substance-card"
    
    st.markdown(f"""
    <div class="{card_class}">
        <h3>{icon} {substance_name.replace('_', '/')}</h3>
        <p>{description}</p>
    </div>
    """, unsafe_allow_html=True)
    
    return st.button(
        f"Assess {substance_name.replace('_', '/')}", 
        key=f"select_{substance_name.lower()}", 
        use_container_width=True
    )

def render_severity_indicator(criteria_count: int, category: str = "") -> None:
    """Render DSM-5 severity indicator with modern styling"""
    severity = get_dsm5_severity(criteria_count)
    severity_class = get_severity_class(severity)
    
    st.markdown(f"""
    <div class="severity-indicator {severity_class}">
        <strong>{severity}</strong><br>
        <small>({criteria_count} criteria met)</small>
    </div>
    """, unsafe_allow_html=True)

def create_enhanced_multiselect(label: str, options: List[str], 
                              default_values: Optional[List[str]] = None,
                              help_text: Optional[str] = None,
                              key: Optional[str] = None) -> List[str]:
    """Create an enhanced multiselect with better UX"""
    if help_text:
        st.markdown(f"**{label}** ℹ️", help=help_text)
        return st.multiselect(
            "",
            options,
            default=default_values or [],
            key=key,
            label_visibility="collapsed"
        )
    else:
        return st.multiselect(
            label,
            options,
            default=default_values or [],
            key=key
        )

def show_clinical_guidance(category: str, guidance_type: str = "substance") -> None:
    """Show clinical guidance for different categories"""
    if guidance_type == "substance" and category in DSM5_SUBSTANCE_CATEGORIES:
        guidance_content = {
            "Alcohol": "DSM-5 requires 2+ criteria within 12 months. Consider tolerance, withdrawal, and impairment.",
            "Cannabis": "Cannabis withdrawal now recognized in DSM-5. Look for irritability and anxiety.",
            "Stimulants": "Include both illicit and prescription stimulants. Route affects severity.",
            "Opioids": "High overdose risk. Document naloxone use and injection complications.",
            "Sedatives_Hypnotics_Anxiolytics": "Dangerous withdrawal. Assess prescribed vs illicit use."
        }
        
        if category in guidance_content:
            st.markdown(f"""
            <div class="clinical-guidance">
                <h4>🩺 Clinical Note</h4>
                <p>{guidance_content[category]}</p>
            </div>
            """, unsafe_allow_html=True)

def create_progress_indicator(current_section: int, total_sections: int, 
                            completed_sections: List[int]) -> None:
    """Create a visual progress indicator"""
    progress_percentage = (current_section + 1) / total_sections
    completion_percentage = len(completed_sections) / total_sections
    
    st.markdown(f"""
    <div class="progress-container">
        <h4>📊 Assessment Progress</h4>
        <p><strong>Current Section:</strong> {current_section + 1}/{total_sections} ({progress_percentage:.0%})</p>
        <p><strong>Data Completion:</strong> {len(completed_sections)}/{total_sections} sections ({completion_percentage:.0%})</p>
    </div>
    """, unsafe_allow_html=True)
    
    st.progress(progress_percentage)

def show_auto_save_indicator():
    """Show auto-save success indicator"""
    st.markdown("""
    <div class="auto-save-indicator">
        ✅ Auto-saved
    </div>
    """, unsafe_allow_html=True)
