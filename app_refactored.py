"""
Refactored Psychiatric Assessment System

A modular, fast, and user-friendly medical data collection application
with enhanced UI/UX and fixed checklist behavior issues.
"""

import streamlit as st
import datetime
import time
from typing import Dict, Any, List

# Import modular components
from ui_components import render_modern_css, create_progress_indicator, show_auto_save_indicator
from section_handlers import (
    render_demographics_section,
    render_chief_complaint_section,
    render_history_present_illness_section,
    render_past_psychiatric_history_section,
    render_past_medical_history_section,
    render_family_history_section,
    render_social_developmental_history_section,
    render_mental_state_examination_section,
    render_cognitive_assessment_section,
    render_risk_assessment_section,
    render_laboratory_investigations_section,
    render_clinical_scales_section,
    render_diagnostic_formulation_section,
    render_treatment_planning_section,
    render_follow_up_monitoring_section
)
from substance_section import render_substance_use_section
from database import create_tables, save_assessment_data, save_patient_data, get_all_patients, load_patient_assessments

# Configure page
st.set_page_config(
    page_title="Psychiatric Assessment System",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': None,
        'Report a bug': None,
        'About': "Enhanced Psychiatric Assessment System v3.0"
    }
)

# Initialize session state
def initialize_session_state():
    """Initialize session state with default values"""
    defaults = {
        'patient_data': {},
        'current_section': 0,
        'patient_id': None,
        'assessment_start_time': datetime.datetime.now(),
        'last_auto_save': time.time(),
        'auto_save_enabled': True,
        'current_view': 'assessment',
        'patient_code_entered': False,
        'auto_save_indicator': False
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

def main():
    """Main application entry point"""
    # Initialize session state and database
    initialize_session_state()
    create_tables()
    
    # Render modern CSS
    render_modern_css()
    
    # Show appropriate view
    if st.session_state.current_view == 'dashboard':
        show_dashboard_view()
    else:
        show_assessment_view()

def show_assessment_view():
    """Display the main assessment interface"""
    # Sidebar navigation
    render_sidebar_navigation()
    
    # Handle patient code input
    if not st.session_state.patient_id:
        render_patient_code_entry()
        return
    
    # Main header
    st.markdown('<h1 class="main-header">🧠 Enhanced Psychiatric Assessment System</h1>', unsafe_allow_html=True)
    
    # Auto-save indicator
    if st.session_state.auto_save_indicator:
        show_auto_save_indicator()
        st.session_state.auto_save_indicator = False
    
    # Render current section
    render_current_section()

def render_sidebar_navigation():
    """Render enhanced sidebar navigation"""
    with st.sidebar:
        st.markdown("### 🧠 Assessment Navigation")
        
        # Patient info
        if st.session_state.patient_id:
            st.markdown(f"**Patient Code:** `{st.session_state.patient_id}`")
        else:
            st.markdown("**Patient Code:** *Not set*")
        
        st.markdown("---")
        
        # Section definitions
        sections = [
            "👤 Demographics & Identifying Information",
            "📝 Chief Complaint & Referral", 
            "📋 History of Present Illness",
            "🏥 Past Psychiatric History",
            "🏥 Past Medical History", 
            "👨‍👩‍👧‍👦 Family History",
            "🏠 Social & Developmental History",
            "🧪 Substance Use Assessment",
            "🧠 Mental State Examination",
            "🧩 Cognitive Assessment", 
            "⚠️ Risk Assessment",
            "🔬 Laboratory & Investigations",
            "📊 Clinical Scales & Ratings",
            "🎯 Diagnostic Formulation",
            "💊 Treatment Planning",
            "📅 Follow-up & Monitoring"
        ]
        
        # Section navigation with completion indicators
        st.markdown("### 📋 Section Navigation")
        
        for i, section in enumerate(sections):
            # Check completion status
            section_keys = [
                "demographics", "chief_complaint", "history_present_illness", "past_psychiatric_history",
                "past_medical_history", "family_history", "social_developmental_history", "substance_use",
                "mental_state_examination", "cognitive_assessment", "risk_assessment", "laboratory_investigations",
                "clinical_scales", "diagnostic_formulation", "treatment_planning", "follow_up_monitoring"
            ]
            
            data_key = section_keys[i] if i < len(section_keys) else None
            is_completed = data_key and data_key in st.session_state.patient_data and st.session_state.patient_data.get(data_key)
            is_current = i == st.session_state.current_section
            
            # Status indicator
            if is_completed:
                status_icon = "✅"
            elif is_current and st.session_state.patient_id:
                status_icon = "📝"
            else:
                status_icon = "⭕"
            
            # Navigation button
            section_label = f"{status_icon} {i+1:2d}. {section.split(' ', 1)[1]}"  # Remove emoji from label
            
            if st.button(
                section_label, 
                key=f"nav_section_{i}",
                disabled=not st.session_state.patient_id,
                use_container_width=True
            ):
                st.session_state.current_section = i
                st.rerun()
        
        # Progress overview
        render_progress_overview(sections)
        
        # Assessment management
        render_assessment_management()

def render_progress_overview(sections: List[str]):
    """Render progress overview in sidebar"""
    st.markdown("---")
    st.markdown("### 📊 Progress Overview")
    
    progress = (st.session_state.current_section + 1) / len(sections)
    st.progress(progress)
    st.write(f"**Current:** Section {st.session_state.current_section + 1}/{len(sections)} ({progress:.0%})")
    
    # Data completion
    completed_sections = len([k for k in st.session_state.patient_data.keys() if st.session_state.patient_data.get(k)])
    completion_rate = completed_sections / len(sections)
    st.write(f"**Completed:** {completed_sections}/{len(sections)} sections ({completion_rate:.0%})")

def render_assessment_management():
    """Render assessment management controls"""
    st.markdown("---")
    st.markdown("### 📁 Assessment Management")
    
    if st.button("🆕 New Assessment"):
        # Reset session state for new assessment
        st.session_state.patient_data = {}
        st.session_state.current_section = 0
        st.session_state.patient_id = None
        st.session_state.assessment_start_time = datetime.datetime.now()
        st.session_state.patient_code_entered = False
        st.success("New assessment started!")
        st.rerun()
    
    if st.button("📊 View Dashboard"):
        st.session_state.current_view = 'dashboard'
        st.rerun()
    
    # Auto-save toggle
    auto_save_enabled = st.checkbox("Enable Auto-save", value=st.session_state.auto_save_enabled)
    st.session_state.auto_save_enabled = auto_save_enabled
    
    # Session info
    st.markdown("---")
    st.markdown("### ⏱️ Session Info")
    st.markdown(f"**Started:** {st.session_state.assessment_start_time.strftime('%Y-%m-%d %H:%M')}")
    duration = datetime.datetime.now() - st.session_state.assessment_start_time
    st.markdown(f"**Duration:** {str(duration).split('.')[0]}")

def render_patient_code_entry():
    """Render patient code entry interface"""
    st.markdown('<h1 class="main-header">🧠 New Psychiatric Assessment</h1>', unsafe_allow_html=True)
    st.markdown('<div class="section-header">Patient Code Entry</div>', unsafe_allow_html=True)
    st.markdown("Please enter a patient code to identify this case (no real names or personal identifiers):")

    col1, col2 = st.columns([3, 1])
    with col1:
        patient_code = st.text_input(
            "Patient Code", 
            placeholder="e.g., CASE-001, PT-ABC123", 
            key="patient_code_input"
        )
    with col2:
        if st.button("Set Code", type="primary"):
            if patient_code.strip():
                st.session_state.patient_id = patient_code.strip().upper()
                st.session_state.patient_code_entered = True
                st.success(f"Patient code set: {st.session_state.patient_id}")
                st.rerun()
            else:
                st.error("Please enter a valid patient code")

    st.info("💡 Use a unique code that helps you identify this case later (e.g., CASE-001, PT-ABC123)")

def render_current_section():
    """Render the current assessment section"""
    section = st.session_state.current_section

    # Section routing - now all sections are implemented
    if section == 0:
        render_demographics_section()
    elif section == 1:
        render_chief_complaint_section()
    elif section == 2:
        render_history_present_illness_section()
    elif section == 3:
        render_past_psychiatric_history_section()
    elif section == 4:
        render_past_medical_history_section()
    elif section == 5:
        render_family_history_section()
    elif section == 6:
        render_social_developmental_history_section()
    elif section == 7:
        render_substance_use_section()
    elif section == 8:
        render_mental_state_examination_section()
    elif section == 9:
        render_cognitive_assessment_section()
    elif section == 10:
        render_risk_assessment_section()
    elif section == 11:
        render_laboratory_investigations_section()
    elif section == 12:
        render_clinical_scales_section()
    elif section == 13:
        render_diagnostic_formulation_section()
    elif section == 14:
        render_treatment_planning_section()
    elif section == 15:
        render_follow_up_monitoring_section()
    else:
        # Fallback for any unexpected section numbers
        st.markdown(f'<div class="section-header">Section {section + 1} - Not Found</div>', unsafe_allow_html=True)
        st.error(f"Section {section + 1} is not available. Please use the navigation to access valid sections.")

def show_dashboard_view():
    """Display dashboard view with patient overview"""
    render_modern_css()
    
    st.markdown('<h1 class="main-header">📊 Assessment Dashboard</h1>', unsafe_allow_html=True)
    
    # Back to assessment button
    if st.button("← Back to Assessment"):
        st.session_state.current_view = 'assessment'
        st.rerun()
    
    # Get all patients
    patients = get_all_patients()
    
    if patients:
        st.markdown("### 👥 Patient Overview")
        
        # Display patient cards
        for patient in patients[:10]:  # Show first 10 patients
            with st.expander(f"Patient: {patient['patient_id']}", expanded=False):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**Age:** {patient.get('age', 'N/A')}")
                    st.write(f"**Gender:** {patient.get('gender', 'N/A')}")
                
                with col2:
                    st.write(f"**Created:** {patient.get('created_at', 'N/A')}")
                    st.write(f"**Updated:** {patient.get('updated_at', 'N/A')}")
                
                with col3:
                    if st.button(f"Load {patient['patient_id']}", key=f"load_{patient['patient_id']}"):
                        # Load patient data
                        assessments = load_patient_assessments(patient['patient_id'])
                        if assessments:
                            # Load most recent assessment
                            latest_assessment = assessments[0]
                            if latest_assessment.get('parsed_data'):
                                st.session_state.patient_data = latest_assessment['parsed_data']
                                st.session_state.patient_id = patient['patient_id']
                                st.session_state.current_view = 'assessment'
                                st.success(f"Loaded assessment for {patient['patient_id']}")
                                st.rerun()
    else:
        st.info("No patient data available. Create an assessment to get started.")

# Auto-save functionality
def auto_save_data():
    """Auto-save assessment data if enabled"""
    if (st.session_state.get('auto_save_enabled', True) and
        st.session_state.get('patient_id') and
        st.session_state.get('patient_data')):

        current_time = time.time()
        last_save = st.session_state.get('last_auto_save', 0)

        # Auto-save every 30 seconds
        if current_time - last_save > 30:
            try:
                save_assessment_data(st.session_state.patient_data)
                save_patient_data(st.session_state.patient_data)
                st.session_state.last_auto_save = current_time
                st.session_state.auto_save_indicator = True
            except Exception as e:
                st.error(f"Auto-save failed: {e}")

def validate_current_data():
    """Validate current assessment data and show any issues"""
    if st.session_state.get('patient_data'):
        from data_models import validate_comprehensive_assessment

        try:
            errors = validate_comprehensive_assessment(st.session_state.patient_data)

            # Show validation errors if any exist
            total_errors = sum(len(error_list) for error_list in errors.values())
            if total_errors > 0:
                with st.sidebar:
                    st.markdown("---")
                    st.markdown("### ⚠️ Data Validation")
                    st.warning(f"{total_errors} validation issue(s) found")

                    if st.button("Show Details", key="show_validation_details"):
                        st.session_state.show_validation = True

                if st.session_state.get('show_validation'):
                    st.markdown("### ⚠️ Data Validation Issues")
                    for category, error_list in errors.items():
                        if error_list:
                            st.error(f"**{category.replace('_', ' ').title()}:**")
                            for error in error_list:
                                st.write(f"• {error}")
        except Exception as e:
            # Silently handle validation errors to not disrupt user experience
            pass

# Run auto-save and validation
auto_save_data()
validate_current_data()

# Main app entry point
if __name__ == "__main__":
    main()
