"""
Enhanced Substance Use Assessment Section

This module provides an improved substance use assessment interface with:
- Fast, intuitive substance selection
- Route of administration, amount/dosage, and duration fields
- Consistent form patterns that prevent dropdown closing issues
- Modern, clean UI design
"""

import streamlit as st
from typing import Dict, Any, List, Optional
from data_models import (
    DSM5_SUBSTANCE_CATEGORIES, 
    FREQUENCY_OPTIONS, 
    DURATION_OPTIONS, 
    ROUTE_OPTIONS,
    AMOUNT_UNITS,
    get_dsm5_severity,
    get_severity_class
)
from ui_components import (
    create_form_section, 
    create_substance_card, 
    render_severity_indicator,
    show_clinical_guidance,
    create_enhanced_multiselect
)

def render_substance_use_section():
    """Render the enhanced substance use assessment section"""
    st.markdown('<div class="section-header">🧪 Substance Use Assessment</div>', unsafe_allow_html=True)
    
    # Initialize substance data
    if 'substance_use' not in st.session_state.patient_data:
        st.session_state.patient_data['substance_use'] = {}
    
    substance_data = st.session_state.patient_data['substance_use']
    
    # Quick screening section
    render_quick_screening(substance_data)
    
    # Detailed assessment if substance use reported
    if substance_data.get('any_substance_use') != "No":
        render_substance_selection_interface(substance_data)
        
        # Show detailed assessment for selected substance
        if 'selected_substance' in st.session_state:
            render_detailed_substance_assessment(
                st.session_state.selected_substance, 
                substance_data
            )
    else:
        st.info("✅ No substance use reported. Assessment complete for this section.")

def render_quick_screening(substance_data: Dict[str, Any]):
    """Render quick screening questions"""
    def screening_form_content():
        col1, col2 = st.columns(2)
        
        with col1:
            any_use = st.selectbox(
                "Any Substance Use (including alcohol/tobacco)",
                ["No", "Yes - past only", "Yes - current"],
                index=0 if not substance_data.get('any_substance_use') else 
                      ["No", "Yes - past only", "Yes - current"].index(substance_data.get('any_substance_use'))
            )
        
        with col2:
            problems = st.selectbox(
                "Substance-Related Problems",
                ["None", "Mild", "Moderate", "Severe"],
                index=0 if not substance_data.get('substance_related_problems') else 
                      ["None", "Mild", "Moderate", "Severe"].index(substance_data.get('substance_related_problems'))
            )
        
        return {'any_substance_use': any_use, 'substance_related_problems': problems}
    
    if create_form_section(
        "🔍 Quick Screening", 
        "quick_screening_form", 
        screening_form_content,
        expanded=True,
        help_text="Start with basic screening questions to determine if detailed assessment is needed."
    ):
        form_data = screening_form_content()
        substance_data.update(form_data)

def render_substance_selection_interface(substance_data: Dict[str, Any]):
    """Render the improved substance selection interface"""
    st.markdown("### 🎯 Substance Categories - Quick Selection")
    st.markdown("Click on any substance category below to assess in detail:")
    
    # Initialize substances dict
    if 'substances' not in substance_data:
        substance_data['substances'] = {}
    
    # Define substance classes with modern icons and descriptions
    substance_classes = {
        "Alcohol": ("🍷", "Beer, wine, spirits, and other alcoholic beverages"),
        "Cannabis": ("🌿", "Marijuana, hash, edibles, concentrates, and synthetic cannabis"),
        "Stimulants": ("⚡", "Cocaine, methamphetamine, amphetamines, and prescription stimulants"),
        "Opioids": ("💊", "Heroin, prescription opioids, fentanyl, and synthetic opioids"),
        "Sedatives_Hypnotics_Anxiolytics": ("😴", "Benzodiazepines, barbiturates, sleep medications"),
        "Hallucinogens": ("🌈", "LSD, PCP, psilocybin, MDMA, ketamine, and synthetic hallucinogens"),
        "Inhalants": ("💨", "Solvents, aerosols, gases, and nitrites"),
        "Tobacco": ("🚬", "Cigarettes, cigars, e-cigarettes, chewing tobacco"),
        "Caffeine": ("☕", "Coffee, tea, energy drinks, caffeine pills"),
        "Other_Unknown": ("❓", "Synthetic drugs, unknown substances, and other substances")
    }
    
    # Display substance cards in a responsive grid
    cols = st.columns(2)
    for i, (substance, (icon, description)) in enumerate(substance_classes.items()):
        with cols[i % 2]:
            # Check if this substance has been assessed
            is_assessed = substance in substance_data['substances'] and substance_data['substances'][substance].get('used')
            
            if create_substance_card(substance, icon, description, is_assessed):
                st.session_state['selected_substance'] = substance
                st.rerun()
    
    # Show summary of assessed substances
    render_substance_summary(substance_data)

def render_detailed_substance_assessment(substance_name: str, substance_data: Dict[str, Any]):
    """Render detailed assessment for selected substance"""
    st.markdown("---")
    st.markdown(f"### 🔍 Detailed Assessment: {substance_name.replace('_', '/')}")
    
    # Initialize substance data
    if substance_name not in substance_data['substances']:
        substance_data['substances'][substance_name] = {'used': False}
    
    data = substance_data['substances'][substance_name]
    
    # Basic usage assessment
    render_basic_usage_form(substance_name, data)
    
    # Enhanced details if substance is used
    if data.get('used'):
        render_usage_details_form(substance_name, data)
        render_dsm5_assessment_form(substance_name, data)
    
    # Back button
    if st.button("← Back to Substance Selection", key="back_to_selection"):
        if 'selected_substance' in st.session_state:
            del st.session_state['selected_substance']
        st.rerun()

def render_basic_usage_form(substance_name: str, data: Dict[str, Any]):
    """Render basic usage assessment form"""
    def basic_usage_content():
        col1, col2 = st.columns(2)
        
        with col1:
            used = st.checkbox(
                f"Has used {substance_name.replace('_', '/')}",
                value=data.get('used', False)
            )
        
        with col2:
            if used:
                current_use = st.selectbox(
                    "Current Use Status",
                    ["Past use only", "Occasional current use", "Regular current use", "Heavy current use"],
                    index=0 if not data.get('current_use') else 
                          ["Past use only", "Occasional current use", "Regular current use", "Heavy current use"].index(data.get('current_use'))
                )
            else:
                current_use = ""
        
        return {'used': used, 'current_use': current_use}
    
    if create_form_section(
        f"Basic Usage - {substance_name.replace('_', '/')}", 
        f"basic_usage_{substance_name.lower()}", 
        basic_usage_content,
        expanded=True
    ):
        form_data = basic_usage_content()
        data.update(form_data)

def render_usage_details_form(substance_name: str, data: Dict[str, Any]):
    """Render detailed usage assessment form with enhanced fields"""
    def usage_details_content():
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # Route of administration
            available_routes = DSM5_SUBSTANCE_CATEGORIES.get(substance_name, {}).get('routes', ROUTE_OPTIONS)
            route = st.selectbox(
                "Route of Administration",
                [""] + available_routes,
                index=0 if not data.get('route') else available_routes.index(data.get('route')) + 1 if data.get('route') in available_routes else 0
            )
            
            # Age of onset
            age_onset = st.number_input(
                "Age of First Use",
                min_value=5, max_value=80,
                value=data.get('age_onset', 18)
            )
        
        with col2:
            # Frequency
            frequency = st.selectbox(
                "Frequency of Use",
                [""] + FREQUENCY_OPTIONS,
                index=0 if not data.get('frequency') else FREQUENCY_OPTIONS.index(data.get('frequency')) + 1 if data.get('frequency') in FREQUENCY_OPTIONS else 0
            )
            
            # Duration of use
            duration = st.selectbox(
                "Duration of Use",
                [""] + DURATION_OPTIONS,
                index=0 if not data.get('duration') else DURATION_OPTIONS.index(data.get('duration')) + 1 if data.get('duration') in DURATION_OPTIONS else 0
            )
        
        with col3:
            # Amount/dosage
            amount_units = AMOUNT_UNITS.get(substance_name.split('_')[0], AMOUNT_UNITS["Other"])
            amount_unit = st.selectbox(
                "Amount Unit",
                [""] + amount_units,
                index=0 if not data.get('amount_unit') else amount_units.index(data.get('amount_unit')) + 1 if data.get('amount_unit') in amount_units else 0
            )
            
            amount_quantity = st.text_input(
                "Typical Amount",
                value=data.get('amount_quantity', ''),
                placeholder="e.g., 2, 0.5, 10"
            )
        
        # Additional details
        st.markdown("#### Additional Details")
        col4, col5 = st.columns(2)
        
        with col4:
            last_use = st.selectbox(
                "Last Use",
                ["", "Today", "Yesterday", "This week", "This month", "1-6 months ago", "More than 6 months ago"],
                index=0 if not data.get('last_use') else ["", "Today", "Yesterday", "This week", "This month", "1-6 months ago", "More than 6 months ago"].index(data.get('last_use')) if data.get('last_use') in ["", "Today", "Yesterday", "This week", "This month", "1-6 months ago", "More than 6 months ago"] else 0
            )
        
        with col5:
            problems = st.checkbox("Has caused problems", value=data.get('problems', False))
        
        return {
            'route': route,
            'age_onset': age_onset,
            'frequency': frequency,
            'duration': duration,
            'amount_unit': amount_unit,
            'amount_quantity': amount_quantity,
            'last_use': last_use,
            'problems': problems
        }
    
    if create_form_section(
        f"Usage Details - {substance_name.replace('_', '/')}", 
        f"usage_details_{substance_name.lower()}", 
        usage_details_content,
        expanded=data.get('used', False),
        help_text="Provide detailed information about usage patterns, routes, and quantities for accurate assessment."
    ):
        form_data = usage_details_content()
        data.update(form_data)

def render_dsm5_assessment_form(substance_name: str, data: Dict[str, Any]):
    """Render DSM-5 criteria assessment form"""
    if not data.get('problems'):
        return
    
    def dsm5_content():
        # Show clinical guidance
        show_clinical_guidance(substance_name, "substance")
        
        # DSM-5 criteria selection
        if substance_name in DSM5_SUBSTANCE_CATEGORIES:
            criteria_options = DSM5_SUBSTANCE_CATEGORIES[substance_name]['dsm5_criteria']
            
            st.markdown("#### DSM-5 Substance Use Disorder Criteria (past 12 months)")
            st.markdown("*Select all criteria that apply:*")
            
            selected_criteria = create_enhanced_multiselect(
                "DSM-5 Criteria",
                criteria_options,
                default_values=data.get('dsm5_criteria', []),
                help_text="Select all criteria that have been present in the past 12 months"
            )
            
            # Additional assessments
            col1, col2 = st.columns(2)
            with col1:
                withdrawal = st.checkbox("Withdrawal symptoms", value=data.get('withdrawal', False))
                tolerance = st.checkbox("Tolerance", value=data.get('tolerance', False))
            
            with col2:
                treatment_history = st.checkbox("Previous treatment attempts", value=data.get('treatment_history', False))
                family_history = st.checkbox("Family history of substance use", value=data.get('family_history', False))
            
            return {
                'dsm5_criteria': selected_criteria,
                'withdrawal': withdrawal,
                'tolerance': tolerance,
                'treatment_history': treatment_history,
                'family_history': family_history
            }
        
        return {}
    
    if create_form_section(
        f"DSM-5 Assessment - {substance_name.replace('_', '/')}", 
        f"dsm5_{substance_name.lower()}", 
        dsm5_content,
        expanded=True,
        help_text="Complete DSM-5 substance use disorder criteria assessment for diagnostic purposes."
    ):
        form_data = dsm5_content()
        data.update(form_data)
        
        # Calculate and display severity
        criteria_count = len(data.get('dsm5_criteria', []))
        data['severity'] = get_dsm5_severity(criteria_count)
        
        st.markdown("#### Severity Assessment")
        render_severity_indicator(criteria_count, substance_name)

def render_substance_summary(substance_data: Dict[str, Any]):
    """Render summary of assessed substances"""
    if 'substances' not in substance_data:
        return
    
    assessed_substances = [
        name for name, data in substance_data['substances'].items() 
        if data.get('used')
    ]
    
    if assessed_substances:
        st.markdown("### 📊 Assessment Summary")
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Substances Assessed", len(assessed_substances))
        
        with col2:
            disorders = [
                name for name, data in substance_data['substances'].items()
                if data.get('used') and len(data.get('dsm5_criteria', [])) >= 2
            ]
            st.metric("Potential Disorders", len(disorders))
        
        # List assessed substances with severity
        st.markdown("#### Assessed Substances:")
        for substance in assessed_substances:
            data = substance_data['substances'][substance]
            severity = data.get('severity', 'Not assessed')
            criteria_count = len(data.get('dsm5_criteria', []))
            
            severity_class = get_severity_class(severity)
            st.markdown(f"""
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; margin: 0.25rem 0; background: #f8f9fa; border-radius: 6px;">
                <span><strong>{substance.replace('_', '/')}</strong></span>
                <span class="severity-indicator {severity_class}" style="margin: 0; padding: 0.25rem 0.75rem; font-size: 0.85rem;">
                    {severity} ({criteria_count} criteria)
                </span>
            </div>
            """, unsafe_allow_html=True)
